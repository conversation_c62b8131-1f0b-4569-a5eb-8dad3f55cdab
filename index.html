<h1>Guess the number!</h1>
<input id="num">
<button onclick="number()">Check</button>
<p id="result">You are right/wrong</p>
<p id="total">Total number of guesses:10 </p>

<script>
    var num=document.getElementById("num");
    var result=document.getElementById("result");
    var total=document.getElementById("total");
    var b=Math.floor(Math.random()*10)+1;
    var count=10;
    
    
    function number(){
        if (count>0){
            var guess=num.value;
        
            if (guess==b){
                result.textContent="Your guess is right!"
            }
            else{
                result.textContent="Your guess is wrong!"
                count--;
            total.textContent="Total number of guesses: "+count
            }
        }else {
            result.textContent = "No more guesses left!";
        }
    
    }


</script>